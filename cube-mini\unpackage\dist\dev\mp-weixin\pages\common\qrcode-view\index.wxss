@charset "UTF-8";
/**
 * uni-app内置的常用样式变量
 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.qrcode-container.data-v-93b2b148 {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.nav-bar.data-v-93b2b148 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #e5e5e5;
}
.nav-bar .nav-left.data-v-93b2b148, .nav-bar .nav-right.data-v-93b2b148 {
  width: 80rpx;
}
.nav-bar .nav-icon.data-v-93b2b148 {
  font-size: 36rpx;
  color: #333;
}
.nav-bar .nav-title.data-v-93b2b148 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}
.qrcode-content.data-v-93b2b148 {
  padding: 40rpx 30rpx;
}
.qrcode-wrapper.data-v-93b2b148 {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  text-align: center;
}
.qrcode-image.data-v-93b2b148 {
  width: 500rpx;
  height: 500rpx;
  margin: 0 auto 40rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 10rpx;
}
.qrcode-tips.data-v-93b2b148 {
  margin: 40rpx 0;
  text-align: left;
}
.qrcode-tips .tip-title.data-v-93b2b148 {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}
.qrcode-tips .tip-item.data-v-93b2b148 {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 40rpx;
  margin-bottom: 10rpx;
}
.action-buttons.data-v-93b2b148 {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}
.action-buttons button.data-v-93b2b148 {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
}
.action-buttons .btn-refresh.data-v-93b2b148 {
  background-color: #007aff;
  color: #fff;
}
.action-buttons .btn-copy.data-v-93b2b148 {
  background-color: #f0f0f0;
  color: #333;
}
.loading-container.data-v-93b2b148, .error-container.data-v-93b2b148 {
  text-align: center;
  padding: 100rpx 0;
}
.loading-container .loading-icon.data-v-93b2b148, .loading-container .error-icon.data-v-93b2b148, .error-container .loading-icon.data-v-93b2b148, .error-container .error-icon.data-v-93b2b148 {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}
.loading-container .loading-text.data-v-93b2b148, .loading-container .error-text.data-v-93b2b148, .error-container .loading-text.data-v-93b2b148, .error-container .error-text.data-v-93b2b148 {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}
.retry-btn.data-v-93b2b148 {
  background-color: #007aff;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  height: 80rpx;
  width: 200rpx;
  font-size: 28rpx;
}
