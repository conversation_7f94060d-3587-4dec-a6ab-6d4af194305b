<view class="qrcode-container data-v-93b2b148"><view class="nav-bar data-v-93b2b148"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="nav-left data-v-93b2b148" bindtap="__e"><text class="nav-icon data-v-93b2b148">←</text></view><view class="nav-title data-v-93b2b148">{{aiName+"登录二维码"}}</view><view class="nav-right data-v-93b2b148"></view></view><view class="qrcode-content data-v-93b2b148"><block wx:if="{{qrUrl}}"><view class="qrcode-wrapper data-v-93b2b148"><image class="qrcode-image data-v-93b2b148" src="{{qrUrl}}" mode="aspectFit" data-event-opts="{{[['error',[['handleImageError',['$event']]]],['load',[['handleImageLoad',['$event']]]]]}}" binderror="__e" bindload="__e"></image><view class="qrcode-tips data-v-93b2b148"><text class="tip-title data-v-93b2b148">扫码登录步骤：</text><text class="tip-item data-v-93b2b148">{{"1. 打开"+aiName+"手机APP"}}</text><text class="tip-item data-v-93b2b148">2. 使用APP扫描上方二维码</text><text class="tip-item data-v-93b2b148">3. 在手机上确认登录</text><text class="tip-item data-v-93b2b148">4. 登录成功后可关闭此页面</text></view><view class="action-buttons data-v-93b2b148"><button data-event-opts="{{[['tap',[['refreshQrCode',['$event']]]]]}}" class="btn-refresh data-v-93b2b148" bindtap="__e">刷新二维码</button><button data-event-opts="{{[['tap',[['copyQrUrl',['$event']]]]]}}" class="btn-copy data-v-93b2b148" bindtap="__e">复制链接</button></view></view></block><block wx:else><block wx:if="{{loading}}"><view class="loading-container data-v-93b2b148"><view class="loading-icon data-v-93b2b148">⏳</view><text class="loading-text data-v-93b2b148">正在加载二维码...</text></view></block><block wx:else><block wx:if="{{error}}"><view class="error-container data-v-93b2b148"><view class="error-icon data-v-93b2b148">❌</view><text class="error-text data-v-93b2b148">二维码加载失败</text><button data-event-opts="{{[['tap',[['retryLoad',['$event']]]]]}}" class="retry-btn data-v-93b2b148" bindtap="__e">重试</button></view></block></block></block></view></view>